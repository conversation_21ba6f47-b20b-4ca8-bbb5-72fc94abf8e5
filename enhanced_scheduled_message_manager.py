#!/usr/bin/env python3
"""
增强版定时消息管理器
支持文本、文件、表情包、URL卡片、混合消息等多种类型
"""

import os
import sys
import json
from datetime import datetime, time
from typing import List, Dict, Any, Optional
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class EnhancedScheduledMessageManager:
    """增强版定时消息管理器"""
    
    def __init__(self):
        # 加载配置
        self.config = load_config()
        db_config = self.config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")
        
        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        self.db.register_plugin_functions("EnhancedScheduledMessageManager", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 初始化表
        try:
            self.db.init_scheduled_message_tables()
            print("✅ 数据库表初始化完成")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")

    def add_text_message(self, task_name: str, chat_name: str, chat_type: str,
                        message_content: str, schedule_config: Dict) -> bool:
        """
        添加文本消息
        
        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            message_content: 文本内容
            schedule_config: 调度配置
        
        schedule_config 示例:
        {
            "type": "daily",
            "time": "09:30",
            "max_count": 10
        }
        或
        {
            "type": "weekly",
            "weekday": 1,  # 0=周一, 6=周日
            "time": "09:30"
        }
        或
        {
            "type": "interval",
            "minutes": 60
        }
        """
        try:
            return self._add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=message_content,
                schedule_config=schedule_config
            )
        except Exception as e:
            print(f"❌ 添加文本消息失败: {e}")
            return False

    def add_file_message(self, task_name: str, chat_name: str, chat_type: str,
                        file_paths: List[str], schedule_config: Dict,
                        message_content: str = None) -> bool:
        """
        添加文件消息
        
        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型
            file_paths: 文件路径列表
            schedule_config: 调度配置
            message_content: 可选的文本内容
        """
        try:
            # 验证文件是否存在
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    raise Exception(f"文件不存在: {file_path}")
            
            return self._add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='file',
                message_content=message_content,
                file_paths=file_paths,
                schedule_config=schedule_config
            )
        except Exception as e:
            print(f"❌ 添加文件消息失败: {e}")
            return False

    def add_emotion_message(self, task_name: str, chat_name: str, chat_type: str,
                           emotion_index: int, schedule_config: Dict,
                           message_content: str = None) -> bool:
        """
        添加表情包消息
        
        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型
            emotion_index: 表情包索引
            schedule_config: 调度配置
            message_content: 可选的文本内容
        """
        try:
            return self._add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='emotion',
                message_content=message_content,
                emotion_index=emotion_index,
                schedule_config=schedule_config
            )
        except Exception as e:
            print(f"❌ 添加表情包消息失败: {e}")
            return False

    def add_url_card_message(self, task_name: str, chat_name: str, chat_type: str,
                            url_content: str, schedule_config: Dict,
                            message_content: str = None) -> bool:
        """
        添加URL卡片消息
        
        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型
            url_content: URL地址
            schedule_config: 调度配置
            message_content: 可选的文本内容
        """
        try:
            return self._add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='url_card',
                message_content=message_content,
                url_content=url_content,
                schedule_config=schedule_config
            )
        except Exception as e:
            print(f"❌ 添加URL卡片消息失败: {e}")
            return False

    def add_mixed_message(self, task_name: str, chat_name: str, chat_type: str,
                         message_order: List[Dict], schedule_config: Dict) -> bool:
        """
        添加混合消息
        
        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型
            message_order: 消息发送顺序配置
            schedule_config: 调度配置
        
        message_order 示例:
        [
            {"type": "text", "content": "早上好！"},
            {"type": "file", "file_paths": ["/path/to/image.jpg"], "delay": 2},
            {"type": "emotion", "emotion_index": 25, "delay": 1},
            {"type": "url_card", "url": "https://example.com"}
        ]
        """
        try:
            # 验证混合消息配置
            for i, item in enumerate(message_order):
                if not isinstance(item, dict) or 'type' not in item:
                    raise Exception(f"混合消息第{i+1}项格式错误")
                
                item_type = item['type']
                if item_type == 'file':
                    file_paths = item.get('file_paths', [])
                    for file_path in file_paths:
                        if not os.path.exists(file_path):
                            raise Exception(f"文件不存在: {file_path}")
            
            return self._add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='mixed',
                message_order=message_order,
                schedule_config=schedule_config
            )
        except Exception as e:
            print(f"❌ 添加混合消息失败: {e}")
            return False

    def _add_scheduled_message(self, task_name: str, chat_name: str, chat_type: str,
                              message_type: str, schedule_config: Dict, **kwargs) -> bool:
        """内部方法：添加定时消息"""
        schedule_type = schedule_config['type']
        
        # 解析调度配置
        schedule_params = {
            'schedule_type': schedule_type,
            'max_send_count': schedule_config.get('max_count')
        }
        
        if schedule_type == 'daily':
            time_str = schedule_config['time']
            hour, minute = map(int, time_str.split(':'))
            schedule_params['schedule_time'] = time(hour, minute)
        
        elif schedule_type == 'weekly':
            time_str = schedule_config['time']
            hour, minute = map(int, time_str.split(':'))
            schedule_params['schedule_time'] = time(hour, minute)
            schedule_params['schedule_weekday'] = schedule_config['weekday']
        
        elif schedule_type == 'monthly':
            time_str = schedule_config['time']
            hour, minute = map(int, time_str.split(':'))
            schedule_params['schedule_time'] = time(hour, minute)
            schedule_params['schedule_day'] = schedule_config['day']
        
        elif schedule_type == 'interval':
            schedule_params['interval_minutes'] = schedule_config['minutes']
        
        elif schedule_type == 'once':
            datetime_str = schedule_config['datetime']
            send_dt = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
            schedule_params['schedule_date'] = send_dt.date()
            schedule_params['schedule_time'] = send_dt.time()
        
        else:
            raise Exception(f"不支持的调度类型: {schedule_type}")
        
        # 合并参数
        all_params = {
            'task_name': task_name,
            'chat_name': chat_name,
            'chat_type': chat_type,
            'message_type': message_type,
            **kwargs,
            **schedule_params
        }
        
        return self.db.add_scheduled_message(**all_params)

    def list_tasks(self) -> List[Dict]:
        """列出所有定时消息任务"""
        try:
            tasks = self.db.get_all_scheduled_messages()
            
            # 解析JSON字段
            for task in tasks:
                if task.get('file_paths'):
                    try:
                        task['file_paths'] = json.loads(task['file_paths'])
                    except:
                        pass
                
                if task.get('message_order'):
                    try:
                        task['message_order'] = json.loads(task['message_order'])
                    except:
                        pass
            
            return tasks
        except Exception as e:
            print(f"❌ 获取任务列表失败: {e}")
            return []

    def remove_task(self, task_name: str) -> bool:
        """删除定时消息任务"""
        try:
            return self.db.delete_scheduled_message(task_name)
        except Exception as e:
            print(f"❌ 删除任务失败: {e}")
            return False

    def enable_task(self, task_name: str, enabled: bool = True) -> bool:
        """启用/禁用任务"""
        try:
            return self.db.update_scheduled_message(task_name, enabled=enabled)
        except Exception as e:
            print(f"❌ 更新任务状态失败: {e}")
            return False

    def get_task_history(self, task_name: str = None, limit: int = 50) -> List[Dict]:
        """获取任务发送历史"""
        try:
            return self.db.get_message_history(task_name, limit)
        except Exception as e:
            print(f"❌ 获取发送历史失败: {e}")
            return []


def main():
    """示例用法"""
    print("🚀 增强版定时消息管理器")
    print("=" * 50)
    
    try:
        manager = EnhancedScheduledMessageManager()
        
        # 示例：添加每日文本消息
        print("📝 添加每日文本消息示例...")
        success = manager.add_text_message(
            task_name="每日问候",
            chat_name="测试群",
            chat_type="group",
            message_content="早上好！新的一天开始了！",
            schedule_config={
                "type": "daily",
                "time": "09:00",
                "max_count": None
            }
        )
        print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 示例：添加混合消息
        print("\n🎭 添加混合消息示例...")
        success = manager.add_mixed_message(
            task_name="早安套餐",
            chat_name="测试群",
            chat_type="group",
            message_order=[
                {"type": "text", "content": "早上好！"},
                {"type": "emotion", "emotion_index": 25, "delay": 2},
                {"type": "text", "content": "今天也要加油哦！", "delay": 1}
            ],
            schedule_config={
                "type": "daily",
                "time": "08:30"
            }
        )
        print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 列出所有任务
        print("\n📋 当前所有任务:")
        tasks = manager.list_tasks()
        for task in tasks:
            print(f"- {task['task_name']} ({task['message_type']}) -> {task['chat_name']}")
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")


if __name__ == "__main__":
    main()
