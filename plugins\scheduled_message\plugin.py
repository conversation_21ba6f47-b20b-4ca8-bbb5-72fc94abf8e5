"""
定时消息发送插件
支持向指定群聊和私聊用户定时发送固定消息
"""

import threading
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from core.plugin_base import Plugin
from .sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS


class ScheduledMessagePlugin(Plugin):
    """
    定时消息发送插件
    支持多种调度类型：一次性、每日、每周、每月、间隔
    """

    priority = 50  # 中等优先级

    def __init__(self, handler):
        super().__init__(handler)

        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("ScheduledMessagePlugin", SCHEDULED_MESSAGE_SQL_FUNCTIONS)

        # 初始化数据库表
        try:
            self.db.init_scheduled_message_tables()
            self.handler._log("✅ 定时消息表初始化完成")
        except Exception as e:
            self.handler._log(f"❌ 初始化定时消息表失败: {e}", level="ERROR")

        # 启动定时检查线程
        self.running = True
        self.check_interval = 30  # 每30秒检查一次
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()

        self.handler._log("✅ 定时消息插件已启动")

    def __del__(self):
        """插件析构时卸载SQL函数并停止调度器"""
        self.running = False
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("ScheduledMessagePlugin")

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        定时消息插件不处理用户消息，只负责定时发送
        """
        return None

    def _scheduler_loop(self):
        """调度器主循环"""
        self.handler._log("🕐 定时消息调度器已启动")

        while self.running:
            try:
                self._check_and_send_messages()
            except Exception as e:
                self.handler._log(f"❌ 定时消息检查异常: {e}", level="ERROR")

            # 等待下次检查
            time.sleep(self.check_interval)

        self.handler._log("🕐 定时消息调度器已停止")

    def _check_and_send_messages(self):
        """检查并发送待发送的消息"""
        try:
            pending_messages = self.db.get_pending_messages()

            if not pending_messages:
                return

            self.handler._log(f"📋 发现 {len(pending_messages)} 条待发送消息")

            for message_task in pending_messages:
                try:
                    self._send_scheduled_message(message_task)
                except Exception as e:
                    self.handler._log(f"❌ 发送定时消息失败: {e}", level="ERROR")
                    # 记录发送失败
                    self.db.update_message_sent(
                        message_task['id'],
                        success=False,
                        error_message=str(e)
                    )

        except Exception as e:
            self.handler._log(f"❌ 检查待发送消息失败: {e}", level="ERROR")

    def _send_scheduled_message(self, message_task: Dict):
        """发送单条定时消息"""
        task_name = message_task['task_name']
        chat_name = message_task['chat_name']
        chat_type = message_task['chat_type']
        message_type = message_task['message_type']

        try:
            # 获取微信实例
            wx = getattr(self.handler, 'wx', None)
            if not wx:
                raise Exception("微信实例不可用")

            # 发送消息
            if chat_type == 'group':
                self.handler._log(f"📤 向群聊 '{chat_name}' 发送定时消息: {task_name} (类型: {message_type})")
            else:
                self.handler._log(f"📤 向私聊 '{chat_name}' 发送定时消息: {task_name} (类型: {message_type})")

            # 根据消息类型发送不同内容
            if message_type == 'text':
                self._send_text_message(wx, chat_name, message_task)
            elif message_type == 'file':
                self._send_file_message(wx, chat_name, message_task)
            elif message_type == 'emotion':
                self._send_emotion_message(wx, chat_name, message_task)
            elif message_type == 'url_card':
                self._send_url_card_message(wx, chat_name, message_task)
            elif message_type == 'mixed':
                self._send_mixed_message(wx, chat_name, message_task)
            else:
                raise Exception(f"不支持的消息类型: {message_type}")

            # 记录发送成功
            self.db.update_message_sent(message_task['id'], success=True)

            self.handler._log(f"✅ 定时消息发送成功: {task_name} -> {chat_name}")

        except Exception as e:
            self.handler._log(f"❌ 发送定时消息失败 [{task_name}]: {e}", level="ERROR")
            # 记录发送失败
            self.db.update_message_sent(
                message_task['id'],
                success=False,
                error_message=str(e)
            )

    def _send_text_message(self, wx, chat_name: str, message_task: Dict):
        """发送文本消息"""
        message_content = message_task['message_content']
        if not message_content:
            raise Exception("文本消息内容为空")

        wx.SendMsg(message_content, who=chat_name, exact=True)

    def _send_file_message(self, wx, chat_name: str, message_task: Dict):
        """发送文件消息"""
        import json

        file_paths_json = message_task['file_paths']
        if not file_paths_json:
            raise Exception("文件路径为空")

        try:
            file_paths = json.loads(file_paths_json)
        except (json.JSONDecodeError, TypeError):
            raise Exception("文件路径格式错误")

        if not isinstance(file_paths, list) or not file_paths:
            raise Exception("文件路径必须是非空列表")

        # 验证文件是否存在
        import os
        for file_path in file_paths:
            if not os.path.exists(file_path):
                raise Exception(f"文件不存在: {file_path}")

        # 发送文件
        wx.SendFiles(file_paths, who=chat_name, exact=True)

        # 如果有文本内容，也一起发送
        message_content = message_task.get('message_content')
        if message_content:
            time.sleep(1)  # 稍微延迟，避免消息发送过快
            wx.SendMsg(message_content, who=chat_name, exact=True)

    def _send_emotion_message(self, wx, chat_name: str, message_task: Dict):
        """发送表情包消息"""
        emotion_index = message_task['emotion_index']
        if emotion_index is None:
            raise Exception("表情包索引为空")

        wx.SendEmotion(emotion_index, who=chat_name, exact=True)

        # 如果有文本内容，也一起发送
        message_content = message_task.get('message_content')
        if message_content:
            time.sleep(1)  # 稍微延迟，避免消息发送过快
            wx.SendMsg(message_content, who=chat_name, exact=True)

    def _send_url_card_message(self, wx, chat_name: str, message_task: Dict):
        """发送URL卡片消息"""
        url_content = message_task['url_content']
        if not url_content:
            raise Exception("URL地址为空")

        # 注意：SendUrlCard的参数是friends列表，不是单个聊天名
        wx.SendUrlCard(url_content, [chat_name])

        # 如果有文本内容，也一起发送
        message_content = message_task.get('message_content')
        if message_content:
            time.sleep(1)  # 稍微延迟，避免消息发送过快
            wx.SendMsg(message_content, who=chat_name, exact=True)

    def _send_mixed_message(self, wx, chat_name: str, message_task: Dict):
        """发送混合消息"""
        import json

        message_order_json = message_task['message_order']
        if not message_order_json:
            raise Exception("混合消息顺序配置为空")

        try:
            message_order = json.loads(message_order_json)
        except (json.JSONDecodeError, TypeError):
            raise Exception("混合消息顺序格式错误")

        if not isinstance(message_order, list) or not message_order:
            raise Exception("混合消息顺序必须是非空列表")

        # 按顺序发送不同类型的消息
        for i, item in enumerate(message_order):
            if not isinstance(item, dict) or 'type' not in item:
                raise Exception(f"混合消息第{i+1}项格式错误")

            item_type = item['type']

            if item_type == 'text':
                content = item.get('content', message_task.get('message_content'))
                if content:
                    wx.SendMsg(content, who=chat_name, exact=True)

            elif item_type == 'file':
                file_paths = item.get('file_paths')
                if file_paths:
                    # 验证文件是否存在
                    import os
                    for file_path in file_paths:
                        if not os.path.exists(file_path):
                            raise Exception(f"文件不存在: {file_path}")
                    wx.SendFiles(file_paths, who=chat_name, exact=True)

            elif item_type == 'emotion':
                emotion_index = item.get('emotion_index')
                if emotion_index is not None:
                    wx.SendEmotion(emotion_index, who=chat_name, exact=True)

            elif item_type == 'url_card':
                url = item.get('url')
                if url:
                    wx.SendUrlCard(url, [chat_name])

            else:
                self.handler._log(f"⚠️ 混合消息中不支持的类型: {item_type}", level="WARNING")

            # 消息间隔
            if i < len(message_order) - 1:  # 不是最后一条消息
                delay = item.get('delay', 1)  # 默认1秒间隔
                time.sleep(delay)

    # ==================== 管理接口 ==================== #

    def add_daily_text_message(self, task_name: str, chat_name: str, chat_type: str,
                              message_content: str, send_time: str, max_send_count: int = None) -> bool:
        """
        添加每日定时文本消息

        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            message_content: 消息内容
            send_time: 发送时间 (格式: "HH:MM", 如 "09:30")
            max_send_count: 最大发送次数 (None表示无限制)
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                schedule_type='daily',
                message_content=message_content,
                schedule_time=schedule_time,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每日定时文本消息失败: {e}", level="ERROR")
            return False

    def add_daily_file_message(self, task_name: str, chat_name: str, chat_type: str,
                              file_paths: list, send_time: str, message_content: str = None,
                              max_send_count: int = None) -> bool:
        """
        添加每日定时文件消息

        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            file_paths: 文件路径列表
            send_time: 发送时间 (格式: "HH:MM", 如 "09:30")
            message_content: 可选的文本内容
            max_send_count: 最大发送次数 (None表示无限制)
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='file',
                schedule_type='daily',
                message_content=message_content,
                file_paths=file_paths,
                schedule_time=schedule_time,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每日定时文件消息失败: {e}", level="ERROR")
            return False

    def add_daily_emotion_message(self, task_name: str, chat_name: str, chat_type: str,
                                 emotion_index: int, send_time: str, message_content: str = None,
                                 max_send_count: int = None) -> bool:
        """
        添加每日定时表情包消息

        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            emotion_index: 表情包索引
            send_time: 发送时间 (格式: "HH:MM", 如 "09:30")
            message_content: 可选的文本内容
            max_send_count: 最大发送次数 (None表示无限制)
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='emotion',
                schedule_type='daily',
                message_content=message_content,
                emotion_index=emotion_index,
                schedule_time=schedule_time,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每日定时表情包消息失败: {e}", level="ERROR")
            return False

    def add_daily_url_card_message(self, task_name: str, chat_name: str, chat_type: str,
                                  url_content: str, send_time: str, message_content: str = None,
                                  max_send_count: int = None) -> bool:
        """
        添加每日定时URL卡片消息

        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            url_content: URL地址
            send_time: 发送时间 (格式: "HH:MM", 如 "09:30")
            message_content: 可选的文本内容
            max_send_count: 最大发送次数 (None表示无限制)
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='url_card',
                schedule_type='daily',
                message_content=message_content,
                url_content=url_content,
                schedule_time=schedule_time,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每日定时URL卡片消息失败: {e}", level="ERROR")
            return False

    def add_daily_mixed_message(self, task_name: str, chat_name: str, chat_type: str,
                               message_order: list, send_time: str, max_send_count: int = None) -> bool:
        """
        添加每日定时混合消息

        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            message_order: 消息发送顺序配置列表
            send_time: 发送时间 (格式: "HH:MM", 如 "09:30")
            max_send_count: 最大发送次数 (None表示无限制)

        message_order 示例:
        [
            {"type": "text", "content": "早上好！"},
            {"type": "file", "file_paths": ["/path/to/image.jpg"], "delay": 2},
            {"type": "emotion", "emotion_index": 25, "delay": 1},
            {"type": "url_card", "url": "https://example.com"}
        ]
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='mixed',
                schedule_type='daily',
                message_order=message_order,
                schedule_time=schedule_time,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每日定时混合消息失败: {e}", level="ERROR")
            return False

    def add_weekly_message(self, task_name: str, chat_name: str, chat_type: str,
                          message_content: str, weekday: int, send_time: str,
                          max_send_count: int = None) -> bool:
        """
        添加每周定时消息

        Args:
            weekday: 星期几 (0=周一, 6=周日)
            其他参数同 add_daily_message
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message_content,
                schedule_type='weekly',
                schedule_time=schedule_time,
                schedule_weekday=weekday,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每周定时消息失败: {e}", level="ERROR")
            return False

    def add_interval_message(self, task_name: str, chat_name: str, chat_type: str,
                           message_content: str, interval_minutes: int,
                           max_send_count: int = None) -> bool:
        """
        添加间隔定时消息

        Args:
            interval_minutes: 间隔分钟数
            其他参数同 add_daily_message
        """
        try:
            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message_content,
                schedule_type='interval',
                interval_minutes=interval_minutes,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加间隔定时消息失败: {e}", level="ERROR")
            return False

    def add_once_message(self, task_name: str, chat_name: str, chat_type: str,
                        message_content: str, send_datetime: str,
                        max_send_count: int = 1) -> bool:
        """
        添加一次性定时消息

        Args:
            send_datetime: 发送时间 (格式: "YYYY-MM-DD HH:MM", 如 "2024-12-25 09:30")
            其他参数同 add_daily_message
        """
        try:
            send_dt = datetime.strptime(send_datetime, "%Y-%m-%d %H:%M")

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message_content,
                schedule_type='once',
                schedule_date=send_dt.date(),
                schedule_time=send_dt.time(),
                max_send_count=max_send_count or 1
            )
        except Exception as e:
            self.handler._log(f"❌ 添加一次性定时消息失败: {e}", level="ERROR")
            return False

    def remove_scheduled_message(self, task_name: str) -> bool:
        """删除定时消息任务"""
        try:
            return self.db.delete_scheduled_message(task_name)
        except Exception as e:
            self.handler._log(f"❌ 删除定时消息失败: {e}", level="ERROR")
            return False

    def enable_scheduled_message(self, task_name: str, enabled: bool = True) -> bool:
        """启用/禁用定时消息任务"""
        try:
            return self.db.update_scheduled_message(task_name, enabled=enabled)
        except Exception as e:
            self.handler._log(f"❌ 更新定时消息状态失败: {e}", level="ERROR")
            return False

    def get_all_tasks(self) -> List[Dict]:
        """获取所有定时消息任务"""
        try:
            return self.db.get_all_scheduled_messages()
        except Exception as e:
            self.handler._log(f"❌ 获取定时消息任务失败: {e}", level="ERROR")
            return []

    def get_task_history(self, task_name: str = None, limit: int = 50) -> List[Dict]:
        """获取任务发送历史"""
        try:
            return self.db.get_message_history(task_name, limit)
        except Exception as e:
            self.handler._log(f"❌ 获取发送历史失败: {e}", level="ERROR")
            return []
