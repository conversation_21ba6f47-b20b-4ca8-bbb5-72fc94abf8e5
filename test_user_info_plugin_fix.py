#!/usr/bin/env python3
"""
测试修复后的用户信息插件
"""

import sys
import traceback
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class MockHandler:
    """模拟主处理程序"""
    
    def __init__(self):
        # 加载配置并初始化数据库连接
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")

        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 模拟微信实例
        self.wx = None
        
    def _log(self, msg: str, level: str = 'INFO'):
        print(f"[MockHandler] [{level}] {msg}")


def test_user_info_plugin_initialization():
    """测试用户信息插件初始化"""
    print("🔍 测试用户信息插件初始化...")
    
    try:
        # 创建模拟handler
        handler = MockHandler()
        print("✅ MockHandler 创建成功")
        
        # 导入并实例化用户信息插件
        from plugins.user_info.plugin import WeChatUserInfoPlugin
        
        print("📝 开始实例化用户信息插件...")
        plugin = WeChatUserInfoPlugin(handler)
        print("✅ 用户信息插件实例化成功")
        
        # 测试插件方法
        print("🔍 测试插件方法...")
        
        # 测试获取状态
        status = plugin.get_collection_status()
        print(f"✅ 获取状态成功: {status}")
        
        # 测试设置配置
        plugin.set_collection_config(force_on_startup=False, interval_hours=12)
        print("✅ 设置配置成功")
        
        # 测试消息处理接口
        result = plugin.on_messages("测试群", ["测试消息"])
        print(f"✅ 消息处理接口测试成功: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户信息插件测试失败: {e}")
        traceback.print_exc()
        return False


def test_database_connection_resilience():
    """测试数据库连接弹性"""
    print("\n🔍 测试数据库连接弹性...")
    
    try:
        # 创建模拟handler
        handler = MockHandler()
        
        # 导入并实例化用户信息插件
        from plugins.user_info.plugin import WeChatUserInfoPlugin
        plugin = WeChatUserInfoPlugin(handler)
        
        # 测试连接检查
        print("🔍 测试连接检查...")
        result = plugin._ensure_db_connection()
        print(f"✅ 连接检查结果: {result}")
        
        # 测试安全数据库操作
        print("🔍 测试安全数据库操作...")
        config = plugin._safe_db_operation(
            "测试获取配置",
            lambda: plugin.db.get_user_data_config()
        )
        print(f"✅ 安全数据库操作成功: {config is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接弹性测试失败: {e}")
        traceback.print_exc()
        return False


def test_plugin_loading_compatibility():
    """测试插件加载兼容性"""
    print("\n🔍 测试插件加载兼容性...")
    
    try:
        # 模拟插件加载过程
        from plugins import load_plugins
        
        # 创建模拟handler
        handler = MockHandler()
        
        print("📝 开始加载所有插件...")
        plugins = load_plugins(handler)
        
        # 查找用户信息插件
        user_info_plugin = None
        for plugin in plugins:
            if plugin.__class__.__name__ == 'WeChatUserInfoPlugin':
                user_info_plugin = plugin
                break
        
        if user_info_plugin:
            print("✅ 用户信息插件加载成功")
            
            # 测试插件接口
            result = user_info_plugin.on_messages("测试", ["测试"])
            print(f"✅ 插件接口测试成功: {result}")
            
            return True
        else:
            print("❌ 未找到用户信息插件")
            return False
        
    except Exception as e:
        print(f"❌ 插件加载兼容性测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 用户信息插件修复验证测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试插件初始化
    if test_user_info_plugin_initialization():
        success_count += 1
    
    # 测试数据库连接弹性
    if test_database_connection_resilience():
        success_count += 1
    
    # 测试插件加载兼容性
    if test_plugin_loading_compatibility():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！用户信息插件修复成功")
        print("\n💡 修复说明:")
        print("- 添加了数据库连接检查和重连机制")
        print("- 使用安全的数据库操作包装器")
        print("- 增强了错误处理和重试逻辑")
        print("- 解决了 'MySQL Connection not available' 错误")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    main()
