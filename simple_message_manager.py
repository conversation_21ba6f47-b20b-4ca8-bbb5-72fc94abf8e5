#!/usr/bin/env python3
"""
定时消息管理器 - 简化版
"""

import sys
from datetime import datetime, time, timedelta
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class ScheduledMessageManager:
    """定时消息管理器"""
    
    def __init__(self):
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        self.db.register_plugin_functions("MessageManager", SCHEDULED_MESSAGE_SQL_FUNCTIONS)

    def list_tasks(self):
        """列出所有任务"""
        print("\n📋 所有定时消息任务:")
        print("=" * 80)
        
        tasks = self.db.get_all_scheduled_messages()
        if not tasks:
            print("暂无定时消息任务")
            return
        
        for i, task in enumerate(tasks, 1):
            status = "🟢 启用" if task['enabled'] else "🔴 禁用"
            next_send = task['next_send_at'] or "无"
            
            print(f"{i}. {task['task_name']}")
            print(f"   目标: {task['chat_name']} ({'群聊' if task['chat_type'] == 'group' else '私聊'})")
            print(f"   类型: {task['message_type']} | 调度: {task['schedule_type']}")
            print(f"   状态: {status} | 发送次数: {task['send_count']}")
            print(f"   下次发送: {next_send}")
            if task['message_content']:
                content = task['message_content'][:50] + "..." if len(task['message_content']) > 50 else task['message_content']
                print(f"   内容: {content}")
            print("-" * 80)

    def add_daily_text(self, task_name: str, chat_name: str, chat_type: str, content: str, send_time: str):
        """添加每日文本消息"""
        try:
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=content,
                schedule_type='daily',
                schedule_time=schedule_time
            )
            
            if success:
                print(f"✅ 每日消息任务 '{task_name}' 添加成功")
            else:
                print(f"❌ 添加任务失败")
                
        except Exception as e:
            print(f"❌ 添加任务失败: {e}")

    def add_once_text(self, task_name: str, chat_name: str, chat_type: str, content: str, send_datetime: str):
        """添加一次性文本消息"""
        try:
            send_dt = datetime.strptime(send_datetime, "%Y-%m-%d %H:%M")
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=content,
                schedule_type='once',
                schedule_date=send_dt.date(),
                schedule_time=send_dt.time()
            )
            
            if success:
                print(f"✅ 一次性消息任务 '{task_name}' 添加成功")
            else:
                print(f"❌ 添加任务失败")
                
        except Exception as e:
            print(f"❌ 添加任务失败: {e}")

    def delete_task(self, task_name: str):
        """删除任务"""
        try:
            success = self.db.delete_scheduled_message(task_name)
            if success:
                print(f"✅ 任务 '{task_name}' 删除成功")
            else:
                print(f"❌ 任务 '{task_name}' 不存在或删除失败")
        except Exception as e:
            print(f"❌ 删除任务失败: {e}")

    def toggle_task(self, task_name: str, enabled: bool):
        """启用/禁用任务"""
        try:
            success = self.db.update_scheduled_message(task_name, enabled=enabled)
            if success:
                status = "启用" if enabled else "禁用"
                print(f"✅ 任务 '{task_name}' {status}成功")
            else:
                print(f"❌ 任务 '{task_name}' 不存在或操作失败")
        except Exception as e:
            print(f"❌ 操作失败: {e}")

    def show_history(self, limit: int = 10):
        """显示发送历史"""
        print(f"\n📊 最近 {limit} 条发送历史:")
        print("=" * 80)
        
        history = self.db.get_message_history(limit=limit)
        if not history:
            print("暂无发送历史")
            return
        
        for record in history:
            status_icon = "✅" if record['status'] == 'success' else "❌"
            print(f"{status_icon} {record['sent_at']} - {record.get('task_name', 'Unknown')}")
            print(f"   目标: {record['chat_name']} | 类型: {record['message_type']}")
            if record['error_message']:
                print(f"   错误: {record['error_message']}")
            print("-" * 80)

    def cleanup_completed_tasks(self):
        """清理已完成的一次性任务"""
        try:
            cursor = self.db.connection.cursor()
            cursor.execute("""
                UPDATE scheduled_messages 
                SET enabled = FALSE 
                WHERE schedule_type = 'once' 
                AND send_count > 0 
                AND enabled = TRUE
            """)
            
            affected = cursor.rowcount
            self.db.connection.commit()
            
            print(f"✅ 清理了 {affected} 个已完成的一次性任务")
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'db'):
            self.db.unregister_plugin_functions("MessageManager")


def main():
    """主函数"""
    print("🤖 定时消息管理器")
    print("=" * 60)
    
    try:
        manager = ScheduledMessageManager()
        
        while True:
            print("\n请选择操作:")
            print("1. 查看所有任务")
            print("2. 添加每日文本消息")
            print("3. 添加一次性文本消息")
            print("4. 删除任务")
            print("5. 启用任务")
            print("6. 禁用任务")
            print("7. 查看发送历史")
            print("8. 清理已完成任务")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-8): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                manager.list_tasks()
            elif choice == '2':
                print("\n📝 添加每日文本消息")
                task_name = input("任务名称: ").strip()
                chat_name = input("聊天对象: ").strip()
                chat_type = input("聊天类型 (group/private): ").strip()
                content = input("消息内容: ").strip()
                send_time = input("发送时间 (HH:MM): ").strip()
                manager.add_daily_text(task_name, chat_name, chat_type, content, send_time)
            elif choice == '3':
                print("\n⏰ 添加一次性文本消息")
                task_name = input("任务名称: ").strip()
                chat_name = input("聊天对象: ").strip()
                chat_type = input("聊天类型 (group/private): ").strip()
                content = input("消息内容: ").strip()
                send_datetime = input("发送时间 (YYYY-MM-DD HH:MM): ").strip()
                manager.add_once_text(task_name, chat_name, chat_type, content, send_datetime)
            elif choice == '4':
                task_name = input("请输入要删除的任务名称: ").strip()
                if task_name:
                    manager.delete_task(task_name)
            elif choice == '5':
                task_name = input("请输入要启用的任务名称: ").strip()
                if task_name:
                    manager.toggle_task(task_name, True)
            elif choice == '6':
                task_name = input("请输入要禁用的任务名称: ").strip()
                if task_name:
                    manager.toggle_task(task_name, False)
            elif choice == '7':
                limit = input("显示条数 (默认10): ").strip()
                limit = int(limit) if limit else 10
                manager.show_history(limit)
            elif choice == '8':
                manager.cleanup_completed_tasks()
            else:
                print("❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n👋 程序退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")


if __name__ == "__main__":
    main()
