#!/usr/bin/env python3
"""
稳定启动脚本：专门用于稳定运行微信机器人
"""

import os
import sys
import time
import subprocess
import signal
from datetime import datetime


class StableRunner:
    """稳定运行器"""
    
    def __init__(self):
        self.running = True
        self.process = None
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 收到信号 {signum}，正在停止...")
        self.running = False
        if self.process:
            self.process.terminate()
    
    def _log(self, msg: str, level: str = 'INFO'):
        """日志记录"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{timestamp} [StableRunner] [{level}] {msg}")
    
    def run(self):
        """运行主程序"""
        self._log("🚀 稳定运行器启动")
        
        restart_count = 0
        max_restarts = 10
        
        while self.running and restart_count < max_restarts:
            try:
                # 启动主程序
                self._log(f"启动微信机器人 (第 {restart_count + 1} 次)")
                
                # 使用当前Python解释器运行main.py
                cmd = [sys.executable, "main.py"]
                self.process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )
                
                # 实时输出日志
                while self.running and self.process.poll() is None:
                    line = self.process.stdout.readline()
                    if line:
                        print(line.rstrip())
                    time.sleep(0.1)
                
                # 获取退出码
                exit_code = self.process.returncode
                self._log(f"程序退出，退出码: {exit_code}")
                
                # 判断是否需要重启
                if not self.running:
                    break
                
                if exit_code == 0:
                    self._log("程序正常退出")
                    break
                elif exit_code == -1073740940:  # 堆损坏
                    self._log("检测到堆损坏错误，等待重启...", "WARNING")
                    restart_count += 1
                    time.sleep(10)
                elif exit_code == -1073741819:  # 访问违规
                    self._log("检测到访问违规错误，等待重启...", "WARNING")
                    restart_count += 1
                    time.sleep(10)
                else:
                    self._log(f"未知错误 {exit_code}，等待重启...", "WARNING")
                    restart_count += 1
                    time.sleep(5)
                
            except KeyboardInterrupt:
                self._log("用户中断")
                self.running = False
                break
            except Exception as e:
                self._log(f"启动异常: {e}", "ERROR")
                restart_count += 1
                time.sleep(5)
        
        if restart_count >= max_restarts:
            self._log(f"已达到最大重启次数 {max_restarts}，停止运行", "ERROR")
        
        self._log("🏁 稳定运行器结束")


def main():
    """主函数"""
    runner = StableRunner()
    runner.run()


if __name__ == '__main__':
    main()
