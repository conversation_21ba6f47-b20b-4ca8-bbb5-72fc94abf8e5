#!/usr/bin/env python3
"""
测试增强版定时消息插件
"""

import os
import sys
import json
import traceback
from datetime import datetime, time
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class MockHandler:
    """模拟主处理程序"""
    
    def __init__(self):
        # 加载配置并初始化数据库连接
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")

        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 模拟微信实例
        self.wx = MockWeChatAPI()
        
    def _log(self, msg: str, level: str = 'INFO'):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{timestamp} [MockHandler] [{level}] {msg}")


class MockWeChatAPI:
    """模拟微信API"""
    
    def SendMsg(self, content: str, who: str = None, exact: bool = False):
        print(f"📤 [模拟发送文本] 发送给: {who}, 内容: {content}")
        return True
    
    def SendFiles(self, file_paths, who: str = None, exact: bool = False):
        if isinstance(file_paths, str):
            file_paths = [file_paths]
        print(f"📁 [模拟发送文件] 发送给: {who}, 文件: {file_paths}")
        return True
    
    def SendEmotion(self, emotion_index: int, who: str = None, exact: bool = False):
        print(f"😊 [模拟发送表情] 发送给: {who}, 表情索引: {emotion_index}")
        return True
    
    def SendUrlCard(self, url: str, friends):
        print(f"🔗 [模拟发送卡片] 发送给: {friends}, URL: {url}")
        return True


def test_database_structure():
    """测试数据库结构"""
    print("🔍 测试数据库结构...")
    
    try:
        # 创建模拟handler
        handler = MockHandler()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        handler.db.register_plugin_functions("TestScheduledMessage", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 初始化表
        handler.db.init_scheduled_message_tables()
        print("✅ 数据库表初始化成功")
        
        # 清理
        handler.db.unregister_plugin_functions("TestScheduledMessage")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库结构测试失败: {e}")
        traceback.print_exc()
        return False


def test_add_different_message_types():
    """测试添加不同类型的消息"""
    print("\n🔍 测试添加不同类型的消息...")
    
    try:
        # 创建模拟handler
        handler = MockHandler()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        handler.db.register_plugin_functions("TestScheduledMessage", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 初始化表
        handler.db.init_scheduled_message_tables()
        
        # 测试添加文本消息
        print("📝 测试添加文本消息...")
        success = handler.db.add_scheduled_message(
            task_name="测试文本消息",
            chat_name="测试群",
            chat_type="group",
            message_type="text",
            message_content="这是一条测试文本消息",
            schedule_type="daily",
            schedule_time=time(9, 0)
        )
        print(f"文本消息添加: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试添加文件消息
        print("📁 测试添加文件消息...")
        success = handler.db.add_scheduled_message(
            task_name="测试文件消息",
            chat_name="测试群",
            chat_type="group",
            message_type="file",
            message_content="这是文件说明",
            file_paths=["C:/test1.jpg", "C:/test2.png"],
            schedule_type="daily",
            schedule_time=time(10, 0)
        )
        print(f"文件消息添加: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试添加表情包消息
        print("😊 测试添加表情包消息...")
        success = handler.db.add_scheduled_message(
            task_name="测试表情包消息",
            chat_name="测试群",
            chat_type="group",
            message_type="emotion",
            message_content="发个表情包",
            emotion_index=25,
            schedule_type="daily",
            schedule_time=time(11, 0)
        )
        print(f"表情包消息添加: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试添加URL卡片消息
        print("🔗 测试添加URL卡片消息...")
        success = handler.db.add_scheduled_message(
            task_name="测试URL卡片消息",
            chat_name="测试群",
            chat_type="group",
            message_type="url_card",
            message_content="分享一个链接",
            url_content="https://www.example.com",
            schedule_type="daily",
            schedule_time=time(12, 0)
        )
        print(f"URL卡片消息添加: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试添加混合消息
        print("🎭 测试添加混合消息...")
        message_order = [
            {"type": "text", "content": "早上好！"},
            {"type": "emotion", "emotion_index": 25, "delay": 2},
            {"type": "text", "content": "今天也要加油！", "delay": 1}
        ]
        success = handler.db.add_scheduled_message(
            task_name="测试混合消息",
            chat_name="测试群",
            chat_type="group",
            message_type="mixed",
            message_order=message_order,
            schedule_type="daily",
            schedule_time=time(8, 30)
        )
        print(f"混合消息添加: {'✅ 成功' if success else '❌ 失败'}")
        
        # 查询所有消息
        print("\n📋 查询所有添加的消息...")
        messages = handler.db.get_all_scheduled_messages()
        for msg in messages:
            print(f"- {msg['task_name']} ({msg['message_type']}) -> {msg['chat_name']}")
        
        # 清理
        handler.db.unregister_plugin_functions("TestScheduledMessage")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加消息类型测试失败: {e}")
        traceback.print_exc()
        return False


def test_plugin_functionality():
    """测试插件功能"""
    print("\n🔍 测试插件功能...")
    
    try:
        # 创建模拟handler
        handler = MockHandler()
        
        # 导入并实例化插件
        from plugins.scheduled_message.plugin import ScheduledMessagePlugin
        plugin = ScheduledMessagePlugin(handler)
        
        print("✅ 插件实例化成功")
        
        # 测试添加不同类型的消息
        print("📝 测试插件API...")
        
        # 添加文本消息
        success = plugin.add_daily_text_message(
            task_name="API测试文本",
            chat_name="测试群",
            chat_type="group",
            message_content="通过API添加的文本消息",
            send_time="14:30"
        )
        print(f"API文本消息: {'✅ 成功' if success else '❌ 失败'}")
        
        # 添加文件消息
        success = plugin.add_daily_file_message(
            task_name="API测试文件",
            chat_name="测试群",
            chat_type="group",
            file_paths=["C:/test.jpg"],
            send_time="15:00",
            message_content="通过API添加的文件消息"
        )
        print(f"API文件消息: {'✅ 成功' if success else '❌ 失败'}")
        
        # 添加表情包消息
        success = plugin.add_daily_emotion_message(
            task_name="API测试表情",
            chat_name="测试群",
            chat_type="group",
            emotion_index=30,
            send_time="15:30",
            message_content="通过API添加的表情包消息"
        )
        print(f"API表情包消息: {'✅ 成功' if success else '❌ 失败'}")
        
        # 添加URL卡片消息
        success = plugin.add_daily_url_card_message(
            task_name="API测试卡片",
            chat_name="测试群",
            chat_type="group",
            url_content="https://www.github.com",
            send_time="16:00",
            message_content="通过API添加的URL卡片消息"
        )
        print(f"API URL卡片消息: {'✅ 成功' if success else '❌ 失败'}")
        
        # 添加混合消息
        message_order = [
            {"type": "text", "content": "API混合消息测试"},
            {"type": "emotion", "emotion_index": 20, "delay": 1},
            {"type": "text", "content": "测试完成！", "delay": 1}
        ]
        success = plugin.add_daily_mixed_message(
            task_name="API测试混合",
            chat_name="测试群",
            chat_type="group",
            message_order=message_order,
            send_time="16:30"
        )
        print(f"API混合消息: {'✅ 成功' if success else '❌ 失败'}")
        
        # 获取所有任务
        print("\n📋 获取所有任务...")
        tasks = plugin.get_all_tasks()
        for task in tasks:
            print(f"- {task['task_name']} ({task['message_type']}) -> {task['chat_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件功能测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 增强版定时消息插件测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试数据库结构
    if test_database_structure():
        success_count += 1
    
    # 测试添加不同类型消息
    if test_add_different_message_types():
        success_count += 1
    
    # 测试插件功能
    if test_plugin_functionality():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！定时消息插件重构成功")
        print("\n💡 新功能说明:")
        print("- ✅ 支持文本消息")
        print("- ✅ 支持文件/图片/视频消息")
        print("- ✅ 支持表情包消息")
        print("- ✅ 支持URL卡片消息")
        print("- ✅ 支持混合消息（多种类型组合）")
        print("- ✅ 支持消息间隔控制")
        print("- ✅ 增强的数据库结构")
        print("- ✅ 完善的错误处理")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    main()
