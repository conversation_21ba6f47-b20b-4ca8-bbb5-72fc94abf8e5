"""
Scheduled Message Plugin SQL Functions
包含定时消息插件所需的所有SQL查询函数
"""
from typing import Optional, List, Dict
from datetime import datetime, timedelta, time, date


def init_scheduled_message_tables(self):
    """初始化定时消息相关表"""
    cursor = self.connection.cursor()

    # 创建定时消息任务表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS scheduled_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
            chat_name VARCHAR(100) NOT NULL COMMENT '聊天窗口名称',
            chat_type ENUM('group', 'private') NOT NULL DEFAULT 'group' COMMENT '聊天类型',
            message_type ENUM('text', 'file', 'emotion', 'url_card', 'mixed') NOT NULL DEFAULT 'text' COMMENT '消息类型',
            message_content TEXT NULL COMMENT '文本消息内容',
            schedule_type ENUM('once', 'daily', 'weekly', 'monthly', 'interval') NOT NULL DEFAULT 'daily' COMMENT '调度类型',
            schedule_time TIME NULL COMMENT '每日执行时间(daily类型)',
            schedule_date DATE NULL COMMENT '一次性执行日期(once类型)',
            schedule_weekday TINYINT NULL COMMENT '每周执行星期几(weekly类型,0=周日)',
            schedule_day TINYINT NULL COMMENT '每月执行日期(monthly类型)',
            interval_minutes INT NULL COMMENT '间隔分钟数(interval类型)',
            enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
            last_sent_at DATETIME NULL COMMENT '最后发送时间',
            next_send_at DATETIME NULL COMMENT '下次发送时间',
            send_count INT DEFAULT 0 COMMENT '已发送次数',
            max_send_count INT NULL COMMENT '最大发送次数(NULL表示无限制)',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_task (task_name),
            INDEX idx_next_send (next_send_at, enabled),
            INDEX idx_chat (chat_name, chat_type),
            INDEX idx_message_type (message_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建消息内容详情表（支持多种消息类型）
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS scheduled_message_contents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            message_id INT NOT NULL COMMENT '关联的消息任务ID',
            content_type ENUM('text', 'file', 'emotion', 'url_card') NOT NULL COMMENT '内容类型',
            content_order INT NOT NULL DEFAULT 0 COMMENT '发送顺序',
            text_content TEXT NULL COMMENT '文本内容',
            file_paths JSON NULL COMMENT '文件路径列表',
            emotion_index INT NULL COMMENT '表情索引',
            url_content VARCHAR(500) NULL COMMENT 'URL地址',
            send_delay FLOAT DEFAULT 0 COMMENT '发送延迟(秒)',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_message_id (message_id),
            INDEX idx_content_order (message_id, content_order),
            FOREIGN KEY (message_id) REFERENCES scheduled_messages(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建发送历史表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS scheduled_message_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            task_id INT NOT NULL,
            chat_name VARCHAR(100) NOT NULL,
            chat_type ENUM('group', 'private') NOT NULL,
            message_type ENUM('text', 'file', 'emotion', 'url_card', 'mixed') NOT NULL,
            message_summary TEXT NULL COMMENT '消息摘要',
            sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            status ENUM('success', 'failed', 'partial') NOT NULL DEFAULT 'success',
            error_message TEXT NULL,
            sent_count INT DEFAULT 0 COMMENT '成功发送的内容数量',
            total_count INT DEFAULT 0 COMMENT '总内容数量',
            INDEX idx_task_id (task_id),
            INDEX idx_sent_at (sent_at),
            INDEX idx_status (status),
            FOREIGN KEY (task_id) REFERENCES scheduled_messages(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_pending_messages(self) -> List[Dict]:
    """获取待发送的消息"""
    cursor = self.connection.cursor(dictionary=True)
    now = datetime.now()

    cursor.execute("""
        SELECT * FROM scheduled_messages
        WHERE enabled = TRUE
        AND (next_send_at IS NULL OR next_send_at <= %s)
        AND (max_send_count IS NULL OR send_count < max_send_count)
        ORDER BY next_send_at ASC
    """, (now,))

    return cursor.fetchall()


def get_message_contents(self, message_id: int) -> List[Dict]:
    """获取消息的详细内容"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM scheduled_message_contents
        WHERE message_id = %s
        ORDER BY content_order ASC
    """, (message_id,))

    contents = cursor.fetchall()

    # 处理JSON字段
    for content in contents:
        if content['file_paths']:
            import json
            try:
                content['file_paths'] = json.loads(content['file_paths'])
            except (json.JSONDecodeError, TypeError):
                content['file_paths'] = []

    return contents


def get_all_scheduled_messages(self) -> List[Dict]:
    """获取所有定时消息任务"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT * FROM scheduled_messages
        ORDER BY created_at DESC
    """)
    return cursor.fetchall()


def get_scheduled_message_by_name(self, task_name: str) -> Optional[Dict]:
    """根据任务名称获取定时消息"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT * FROM scheduled_messages
        WHERE task_name = %s
    """, (task_name,))
    return cursor.fetchone()


def add_scheduled_message(self, task_name: str, chat_name: str, chat_type: str,
                         message_type: str, schedule_type: str, contents: List[Dict] = None,
                         message_content: str = None, **kwargs) -> bool:
    """添加定时消息任务

    Args:
        task_name: 任务名称
        chat_name: 聊天窗口名称
        chat_type: 聊天类型 ('group' 或 'private')
        message_type: 消息类型 ('text', 'file', 'emotion', 'url_card', 'mixed')
        schedule_type: 调度类型
        contents: 消息内容列表，格式：
            [
                {
                    'type': 'text',
                    'content': '文本内容',
                    'delay': 0  # 可选，发送延迟秒数
                },
                {
                    'type': 'file',
                    'paths': ['path1', 'path2'],
                    'delay': 1
                },
                {
                    'type': 'emotion',
                    'index': 25,
                    'delay': 0.5
                },
                {
                    'type': 'url_card',
                    'url': 'https://example.com',
                    'delay': 0
                }
            ]
        message_content: 兼容旧版本的文本内容
    """
    cursor = self.connection.cursor()

    # 计算下次发送时间
    next_send_at = calculate_next_send_time(schedule_type, **kwargs)

    # 插入主任务
    cursor.execute("""
        INSERT INTO scheduled_messages
        (task_name, chat_name, chat_type, message_type, message_content, schedule_type,
         schedule_time, schedule_date, schedule_weekday, schedule_day,
         interval_minutes, next_send_at, max_send_count)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        task_name, chat_name, chat_type, message_type, message_content, schedule_type,
        kwargs.get('schedule_time'), kwargs.get('schedule_date'),
        kwargs.get('schedule_weekday'), kwargs.get('schedule_day'),
        kwargs.get('interval_minutes'), next_send_at,
        kwargs.get('max_send_count')
    ))

    message_id = cursor.lastrowid

    # 如果有详细内容，插入到内容表
    if contents:
        for order, content in enumerate(contents):
            content_type = content.get('type')
            delay = content.get('delay', 0)

            text_content = None
            file_paths = None
            emotion_index = None
            url_content = None

            if content_type == 'text':
                text_content = content.get('content')
            elif content_type == 'file':
                import json
                file_paths = json.dumps(content.get('paths', []))
            elif content_type == 'emotion':
                emotion_index = content.get('index')
            elif content_type == 'url_card':
                url_content = content.get('url')

            cursor.execute("""
                INSERT INTO scheduled_message_contents
                (message_id, content_type, content_order, text_content, file_paths,
                 emotion_index, url_content, send_delay)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                message_id, content_type, order, text_content, file_paths,
                emotion_index, url_content, delay
            ))

    self.connection.commit()
    return cursor.rowcount > 0


def update_scheduled_message(self, task_name: str, **kwargs) -> bool:
    """更新定时消息任务"""
    cursor = self.connection.cursor()

    updates = []
    params = []

    for field in ['chat_name', 'chat_type', 'message_content', 'schedule_type',
                  'schedule_time', 'schedule_date', 'schedule_weekday',
                  'schedule_day', 'interval_minutes', 'enabled', 'max_send_count']:
        if field in kwargs:
            updates.append(f"{field} = %s")
            params.append(kwargs[field])

    if not updates:
        return False

    # 如果更新了调度相关字段，重新计算下次发送时间
    if any(field in kwargs for field in ['schedule_type', 'schedule_time', 'schedule_date',
                                        'schedule_weekday', 'schedule_day', 'interval_minutes']):
        # 获取当前任务信息
        task = self.get_scheduled_message_by_name(task_name)
        if task:
            # 合并更新参数
            schedule_params = {
                'schedule_type': kwargs.get('schedule_type', task['schedule_type']),
                'schedule_time': kwargs.get('schedule_time', task['schedule_time']),
                'schedule_date': kwargs.get('schedule_date', task['schedule_date']),
                'schedule_weekday': kwargs.get('schedule_weekday', task['schedule_weekday']),
                'schedule_day': kwargs.get('schedule_day', task['schedule_day']),
                'interval_minutes': kwargs.get('interval_minutes', task['interval_minutes'])
            }
            next_send_at = calculate_next_send_time(**schedule_params)
            updates.append("next_send_at = %s")
            params.append(next_send_at)

    updates.append("updated_at = NOW()")
    params.append(task_name)

    cursor.execute(f"""
        UPDATE scheduled_messages
        SET {', '.join(updates)}
        WHERE task_name = %s
    """, params)

    self.connection.commit()
    return cursor.rowcount > 0


def delete_scheduled_message(self, task_name: str) -> bool:
    """删除定时消息任务"""
    cursor = self.connection.cursor()
    cursor.execute("""
        DELETE FROM scheduled_messages
        WHERE task_name = %s
    """, (task_name,))
    self.connection.commit()
    return cursor.rowcount > 0


def update_message_sent(self, task_id: int, success: bool = True, error_message: str = None):
    """更新消息发送状态"""
    cursor = self.connection.cursor()

    # 更新任务的发送统计
    cursor.execute("""
        UPDATE scheduled_messages
        SET last_sent_at = NOW(),
            send_count = send_count + 1,
            next_send_at = %s
        WHERE id = %s
    """, (calculate_next_send_time_for_task(self, task_id), task_id))

    # 记录发送历史
    cursor.execute("""
        INSERT INTO scheduled_message_history
        (task_id, chat_name, chat_type, message_content, status, error_message)
        SELECT id, chat_name, chat_type, message_content, %s, %s
        FROM scheduled_messages WHERE id = %s
    """, ('success' if success else 'failed', error_message, task_id))

    self.connection.commit()


def calculate_next_send_time(schedule_type: str, **kwargs) -> Optional[datetime]:
    """计算下次发送时间"""
    now = datetime.now()

    if schedule_type == 'once':
        schedule_date = kwargs.get('schedule_date')
        schedule_time = kwargs.get('schedule_time')
        if schedule_date and schedule_time:
            return datetime.combine(schedule_date, schedule_time)
        return None

    elif schedule_type == 'daily':
        schedule_time = kwargs.get('schedule_time')
        if schedule_time:
            next_time = datetime.combine(now.date(), schedule_time)
            if next_time <= now:
                next_time += timedelta(days=1)
            return next_time

    elif schedule_type == 'weekly':
        schedule_weekday = kwargs.get('schedule_weekday')
        schedule_time = kwargs.get('schedule_time')
        if schedule_weekday is not None and schedule_time:
            days_ahead = schedule_weekday - now.weekday()
            if days_ahead <= 0:  # 目标日期已过，计算下周
                days_ahead += 7
            next_date = now.date() + timedelta(days=days_ahead)
            next_time = datetime.combine(next_date, schedule_time)
            if next_time <= now:
                next_time += timedelta(weeks=1)
            return next_time

    elif schedule_type == 'monthly':
        schedule_day = kwargs.get('schedule_day')
        schedule_time = kwargs.get('schedule_time')
        if schedule_day and schedule_time:
            try:
                next_time = datetime.combine(now.replace(day=schedule_day).date(), schedule_time)
                if next_time <= now:
                    # 下个月
                    if now.month == 12:
                        next_time = next_time.replace(year=now.year + 1, month=1)
                    else:
                        next_time = next_time.replace(month=now.month + 1)
                return next_time
            except ValueError:
                # 处理月份天数不足的情况
                pass

    elif schedule_type == 'interval':
        interval_minutes = kwargs.get('interval_minutes')
        if interval_minutes:
            return now + timedelta(minutes=interval_minutes)

    return None


def calculate_next_send_time_for_task(self, task_id: int) -> Optional[datetime]:
    """为特定任务计算下次发送时间"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT schedule_type, schedule_time, schedule_date, schedule_weekday,
               schedule_day, interval_minutes
        FROM scheduled_messages WHERE id = %s
    """, (task_id,))

    task = cursor.fetchone()
    if not task:
        return None

    # 处理schedule_time字段的类型转换
    schedule_time = task['schedule_time']
    if schedule_time is not None:
        # 如果是timedelta类型，转换为time类型
        if isinstance(schedule_time, timedelta):
            total_seconds = int(schedule_time.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            from datetime import time
            schedule_time = time(hours, minutes)
        # 如果是字符串类型，解析为time类型
        elif isinstance(schedule_time, str):
            from datetime import time
            try:
                hour, minute = map(int, schedule_time.split(':'))
                schedule_time = time(hour, minute)
            except (ValueError, AttributeError):
                schedule_time = None

    return calculate_next_send_time(
        task['schedule_type'],
        schedule_time=schedule_time,
        schedule_date=task['schedule_date'],
        schedule_weekday=task['schedule_weekday'],
        schedule_day=task['schedule_day'],
        interval_minutes=task['interval_minutes']
    )


def get_message_history(self, task_name: str = None, limit: int = 100) -> List[Dict]:
    """获取消息发送历史"""
    cursor = self.connection.cursor(dictionary=True)

    if task_name:
        cursor.execute("""
            SELECT h.*, s.task_name
            FROM scheduled_message_history h
            JOIN scheduled_messages s ON h.task_id = s.id
            WHERE s.task_name = %s
            ORDER BY h.sent_at DESC
            LIMIT %s
        """, (task_name, limit))
    else:
        cursor.execute("""
            SELECT h.*, s.task_name
            FROM scheduled_message_history h
            JOIN scheduled_messages s ON h.task_id = s.id
            ORDER BY h.sent_at DESC
            LIMIT %s
        """, (limit,))

    return cursor.fetchall()


# 导出所有SQL函数
SCHEDULED_MESSAGE_SQL_FUNCTIONS = {
    'init_scheduled_message_tables': init_scheduled_message_tables,
    'get_pending_messages': get_pending_messages,
    'get_message_contents': get_message_contents,
    'get_all_scheduled_messages': get_all_scheduled_messages,
    'get_scheduled_message_by_name': get_scheduled_message_by_name,
    'add_scheduled_message': add_scheduled_message,
    'update_scheduled_message': update_scheduled_message,
    'delete_scheduled_message': delete_scheduled_message,
    'update_message_sent': update_message_sent,
    'get_message_history': get_message_history,
}
